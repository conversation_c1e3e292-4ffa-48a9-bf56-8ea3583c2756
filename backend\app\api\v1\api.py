"""
Main API router for v1
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    agents,
    conversations,
    files,
    health,
    dashboard
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(conversations.router, prefix="/conversations", tags=["conversations"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
