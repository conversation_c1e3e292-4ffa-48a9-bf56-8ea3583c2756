"""Dashboard endpoints"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc

from app.core.database import get_async_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.conversation import Conversation, Message, ConversationStatus
from app.models.agent import Agent
from app.models.file import File
from app.services.conversation_service import ConversationService
from app.services.file_service import FileService

router = APIRouter()


@router.get("/activity")
async def get_recent_activity(
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
) -> List[Dict[str, Any]]:
    """Get recent user activity"""
    activities = []
    
    # Get recent conversations
    recent_conversations = await db.execute(
        select(Conversation)
        .where(Conversation.user_id == current_user.id)
        .order_by(desc(Conversation.created_at))
        .limit(5)
    )
    
    for conv in recent_conversations.scalars():
        activities.append({
            "id": f"conv_{conv.id}",
            "type": "conversation_started",
            "title": f"Started conversation: {conv.title}",
            "description": conv.description or "New conversation",
            "timestamp": conv.created_at.isoformat(),
            "status": "success"
        })
    
    # Get recent file uploads
    recent_files = await db.execute(
        select(File)
        .where(File.owner_id == current_user.id)
        .order_by(desc(File.created_at))
        .limit(3)
    )
    
    for file in recent_files.scalars():
        activities.append({
            "id": f"file_{file.id}",
            "type": "file_uploaded",
            "title": f"Uploaded file: {file.filename}",
            "description": f"Size: {file.size} bytes",
            "timestamp": file.created_at.isoformat(),
            "status": "success"
        })
    
    # Get recent messages
    recent_messages = await db.execute(
        select(Message)
        .join(Conversation)
        .where(Conversation.user_id == current_user.id)
        .order_by(desc(Message.created_at))
        .limit(3)
    )
    
    for msg in recent_messages.scalars():
        activities.append({
            "id": f"msg_{msg.id}",
            "type": "message_sent",
            "title": "New message",
            "description": msg.content[:100] + "..." if len(msg.content) > 100 else msg.content,
            "timestamp": msg.created_at.isoformat(),
            "status": "success"
        })
    
    # Sort by timestamp and limit
    activities.sort(key=lambda x: x["timestamp"], reverse=True)
    return activities[:limit]


@router.get("/stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
) -> Dict[str, Any]:
    """Get dashboard statistics"""
    conversation_service = ConversationService(db)
    file_service = FileService(db)
    
    # Get conversation stats
    conv_stats = await conversation_service.get_conversation_stats(current_user.id)
    
    # Get file stats
    file_stats = await file_service.get_file_stats(owner_id=current_user.id)
    
    # Get agent count
    agent_count = await db.scalar(
        select(func.count(Agent.id))
        .where(Agent.owner_id == current_user.id)
    )
    
    # Get recent activity count (last 7 days)
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_activity_count = await db.scalar(
        select(func.count(Conversation.id))
        .where(
            Conversation.user_id == current_user.id,
            Conversation.created_at >= week_ago
        )
    )
    
    return {
        "conversations": conv_stats,
        "files": file_stats,
        "agents": {"total_count": agent_count or 0},
        "recent_activity_count": recent_activity_count or 0
    }