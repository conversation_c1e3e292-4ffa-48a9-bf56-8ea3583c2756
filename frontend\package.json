{"name": "agentico-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "e2e": "playwright test", "e2e:ui": "playwright test --ui"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.0.4", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "socket.io-client": "^4.7.4", "sonner": "^2.0.3", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jsdom": "^23.0.1", "playwright": "^1.40.1", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}