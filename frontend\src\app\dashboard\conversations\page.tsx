'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline';

export default function ConversationsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Conversations</h1>
          <p className="text-gray-600">Manage your AI conversations and chat history</p>
        </div>
        <Button className="flex items-center gap-2">
          <PlusIcon className="h-4 w-4" />
          New Conversation
        </Button>
      </div>

      <div className="grid gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <ChatBubbleLeftIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No conversations yet</h3>
              <p className="text-sm text-gray-600 mb-4">
                Start a new conversation with an AI agent to see it here.
              </p>
              <Button variant="outline">
                Start Conversation
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}