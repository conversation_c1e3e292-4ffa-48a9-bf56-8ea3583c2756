'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusIcon, DocumentIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';

export default function FilesPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Files</h1>
          <p className="text-gray-600">Upload and manage your files for AI processing</p>
        </div>
        <Button className="flex items-center gap-2">
          <PlusIcon className="h-4 w-4" />
          Upload File
        </Button>
      </div>

      <div className="grid gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <DocumentIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No files uploaded</h3>
              <p className="text-sm text-gray-600 mb-4">
                Upload documents, images, or other files to work with AI agents.
              </p>
              <Button variant="outline" className="flex items-center gap-2">
                <CloudArrowUpIcon className="h-4 w-4" />
                Upload Files
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}