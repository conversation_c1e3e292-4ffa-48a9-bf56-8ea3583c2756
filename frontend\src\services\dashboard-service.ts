import { apiClient } from '@/lib/api-client';

export interface Activity {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status: string;
}

export interface DashboardStats {
  conversations: {
    total_count: number;
    active_count: number;
    archived_count: number;
    total_messages: number;
  };
  files: {
    total_count: number;
    total_size: number;
  };
  agents: {
    total_count: number;
  };
  recent_activity_count: number;
}

class DashboardService {
  async getRecentActivity(limit: number = 10): Promise<Activity[]> {
    const response = await apiClient.get('/dashboard/activity', {
      params: { limit }
    });
    return response.data;
  }

  async getDashboardStats(): Promise<DashboardStats> {
    const response = await apiClient.get('/dashboard/stats');
    return response.data;
  }
}

export const dashboardService = new DashboardService();