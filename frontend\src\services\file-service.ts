import { apiClient } from '@/lib/api-client';

export interface FileUpload {
  id: number;
  filename: string;
  file_size: number;
  content_type: string;
  status: string;
  public_url?: string;
  message: string;
}

export interface FileItem {
  id: number;
  filename: string;
  original_filename: string;
  file_type: string;
  mime_type: string;
  file_size: number;
  file_size_mb: number;
  file_hash: string;
  storage_path: string;
  status: string;
  is_public: boolean;
  is_temporary: boolean;
  extracted_text?: string;
  meta_data?: any;
  processing_status?: string;
  processing_error?: string;
  processing_progress: number;
  access_level: string;
  shared_with: number[];
  download_count: number;
  view_count: number;
  expires_at?: string;
  auto_delete: boolean;
  created_at: string;
  updated_at: string;
  last_accessed_at?: string;
  owner_id: number;
  public_url?: string;
}

export interface FileStats {
  total_count: number;
  total_size_bytes: number;
  total_size_mb: number;
  ready_count: number;
  processing_count: number;
  error_count: number;
  document_count: number;
  image_count: number;
  audio_count: number;
  video_count: number;
  code_count: number;
  data_count: number;
  archive_count: number;
  other_count: number;
}

export interface FileListParams {
  skip?: number;
  limit?: number;
  file_type?: string;
  status?: string;
}

export interface FileSearchParams {
  query: string;
  skip?: number;
  limit?: number;
}

class FileService {
  async uploadFile(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<FileUpload> {
    const response = await apiClient.uploadFile('/files/upload', file, onProgress);
    return response.data;
  }

  async getFiles(params?: FileListParams): Promise<FileItem[]> {
    const response = await apiClient.get('/files', { params });
    return response.data;
  }

  async getFile(id: number): Promise<FileItem> {
    const response = await apiClient.get(`/files/${id}`);
    return response.data;
  }

  async deleteFile(id: number): Promise<void> {
    await apiClient.delete(`/files/${id}`);
  }

  async downloadFile(filePath: string): Promise<Blob> {
    const response = await apiClient.get(`/files/download/${filePath}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getFileStats(): Promise<FileStats> {
    const response = await apiClient.get('/files/stats/storage');
    return response.data;
  }

  async searchFiles(params: FileSearchParams): Promise<FileItem[]> {
    const response = await apiClient.get('/files/search', { params });
    return response.data;
  }

  async shareFile(id: number, userId: number): Promise<void> {
    await apiClient.post(`/files/${id}/share`, { user_id: userId });
  }

  async unshareFile(id: number, userId: number): Promise<void> {
    await apiClient.post(`/files/${id}/unshare`, { user_id: userId });
  }

  async makeFilePublic(id: number): Promise<FileItem> {
    const response = await apiClient.post(`/files/${id}/make-public`);
    return response.data;
  }

  async makeFilePrivate(id: number): Promise<FileItem> {
    const response = await apiClient.post(`/files/${id}/make-private`);
    return response.data;
  }

  // Helper methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(fileType: string): string {
    switch (fileType.toLowerCase()) {
      case 'document':
        return '📄';
      case 'image':
        return '🖼️';
      case 'audio':
        return '🎵';
      case 'video':
        return '🎬';
      case 'code':
        return '💻';
      case 'data':
        return '📊';
      case 'archive':
        return '📦';
      default:
        return '📁';
    }
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'ready':
        return 'text-green-600 bg-green-100';
      case 'processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'uploading':
        return 'text-blue-600 bg-blue-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'deleted':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  isVideoFile(mimeType: string): boolean {
    return mimeType.startsWith('video/');
  }

  isAudioFile(mimeType: string): boolean {
    return mimeType.startsWith('audio/');
  }

  isDocumentFile(mimeType: string): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/json',
      'application/xml',
    ];
    return documentTypes.includes(mimeType);
  }

  canPreview(mimeType: string): boolean {
    return (
      this.isImageFile(mimeType) ||
      this.isVideoFile(mimeType) ||
      this.isAudioFile(mimeType) ||
      mimeType === 'application/pdf' ||
      mimeType === 'text/plain'
    );
  }
}

export const fileService = new FileService();
