#!/bin/bash

# Test build script for Agentico platform
echo "Testing Agentico platform build..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running"
    exit 1
fi

# Clean up any existing containers
echo "Cleaning up existing containers..."
docker-compose down --volumes --remove-orphans

# Build and start services
echo "Building and starting services..."
docker-compose up --build -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Check service status
echo "Checking service status..."
docker-compose ps

# Check logs for any errors
echo "Checking backend logs..."
docker-compose logs backend | tail -20

echo "Checking frontend logs..."
docker-compose logs frontend | tail -20

echo "Checking celery-worker logs..."
docker-compose logs celery-worker | tail -20

echo "Build test completed!"
